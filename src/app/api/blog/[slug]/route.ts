import { NextResponse } from 'next/server';
import { getMdxPostBySlug } from '@/lib/mdx-utils';

/**
 * GET /api/blog/[slug]
 * 獲取單一部落格文章（MDX 版本）
 */
export async function GET(
  _request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // 使用 MDX 工具獲取文章
    const mdxPost = getMdxPostBySlug(slug);

    if (!mdxPost) {
      return NextResponse.json(
        { error: '找不到指定的文章' },
        { status: 404 }
      );
    }

    // 轉換為 API 回應格式
    const post = {
      title: mdxPost.frontmatter.title,
      slug: mdxPost.slug,
      content: mdxPost.content,
      publishDate: new Date(mdxPost.frontmatter.publishDate),
      author: mdxPost.frontmatter.author,
      thumbnail: mdxPost.frontmatter.thumbnail,
      tags: mdxPost.frontmatter.tags,
      readingTime: mdxPost.readingTime,
      seoTitle: mdxPost.frontmatter.seoTitle,
      seoDescription: mdxPost.frontmatter.seoDescription,
      socialImage: mdxPost.frontmatter.socialImage,
      socialTitle: mdxPost.frontmatter.socialTitle,
      socialDescription: mdxPost.frontmatter.socialDescription,
      excerpt: mdxPost.frontmatter.excerpt,
      featured: mdxPost.frontmatter.featured,
    };

    const response = NextResponse.json({ post });
    return response;

  } catch (error) {
    console.error('獲取部落格文章詳情失敗:', error);
    return NextResponse.json(
      { error: '無法獲取部落格文章詳情' },
      { status: 500 }
    );
  }
}
