import { NextResponse } from 'next/server';
import { getPaginatedMdxPosts } from '@/lib/mdx-utils';

/**
 * GET /api/blog
 * 獲取所有部落格文章（MDX 版本）
 */

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // 使用 MDX 工具獲取分頁文章
    const result = getPaginatedMdxPosts(page, pageSize);

    if (result.posts.length === 0 && result.total === 0) {
      return NextResponse.json({
        posts: [],
        total: 0,
        hasMore: false,
        message: '沒有找到部落格文章，請檢查 content/blog 目錄'
      });
    }

    // 轉換為 API 回應格式
    const posts = result.posts.map(post => ({
      title: post.frontmatter.title,
      slug: post.slug,
      excerpt: post.excerpt,
      thumbnail: post.frontmatter.thumbnail,
      publishDate: new Date(post.frontmatter.publishDate),
      author: post.frontmatter.author,
      tags: post.frontmatter.tags,
      readingTime: post.readingTime,
      seoTitle: post.frontmatter.seoTitle,
      seoDescription: post.frontmatter.seoDescription,
    }));

    const response = NextResponse.json({
      posts,
      total: result.total,
      hasMore: result.hasMore,
      page: result.page,
      pageSize: result.pageSize
    });

    return response;

  } catch (error) {
    console.error('獲取部落格文章失敗:', error);
    console.error('錯誤詳情:', error instanceof Error ? error.message : String(error));

    return NextResponse.json(
      {
        error: '無法獲取部落格文章',
        details: error instanceof Error ? error.message : String(error),
        suggestions: [
          '1. 確認 content/blog 目錄存在',
          '2. 確認目錄中有 .mdx 檔案',
          '3. 檢查 MDX 檔案的 frontmatter 格式',
          '4. 確認檔案命名格式為 YYYY-MM-DD-slug.mdx'
        ]
      },
      { status: 500 }
    );
  }
}
