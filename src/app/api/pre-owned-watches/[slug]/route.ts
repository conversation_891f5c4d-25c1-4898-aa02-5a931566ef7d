import { NextResponse } from 'next/server';
import { getCachedWatchDetailData } from '@/lib/cache-implementation';
import { transformWatchData, type Watch } from '@/types/watch';

// 手錶資料的 Google Sheets 範圍（詳情頁面需要 A~R 欄位）
const WATCHES_SHEET_RANGE = '手錶庫存!A:R'; // 手錶庫存工作表（包含基本資料 A~L 和 SEO 欄位 M~R）

/**
 * GET /api/pre-owned-watches/[slug]
 * 獲取單一手錶詳情
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // 從快取讀取 Google Sheets 資料
    const rawData = await getCachedWatchDetailData(WATCHES_SHEET_RANGE);

    if (!rawData || rawData.length === 0) {
      return NextResponse.json(
        { error: '找不到手錶資料' },
        { status: 404 }
      );
    }

    // 跳過標題列，轉換資料
    const watches: Watch[] = rawData
      .slice(1) // 跳過標題列
      .map((row, index) => transformWatchData(row, index + 1))
      .filter(watch => watch.productName && watch.brand); // 過濾空資料

    // 尋找指定的手錶（支援 ID 或 slug）
    const watch = watches.find(w => w.id === slug || w.seoSlug === slug);

    if (!watch) {
      return NextResponse.json(
        { error: '找不到指定的手錶' },
        { status: 404 }
      );
    }

    const response = NextResponse.json({ watch });
    return response;

  } catch (error) {
    console.error('獲取手錶詳情失敗:', error);
    return NextResponse.json(
      { error: '無法獲取手錶詳情' },
      { status: 500 }
    );
  }
}
