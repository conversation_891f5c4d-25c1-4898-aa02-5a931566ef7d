import { NextRequest, NextResponse } from 'next/server';
import { getSheetsClient } from '@/lib/google-sheets';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';
import { validateSubmission } from '@/lib/anti-bot';
import { checkRateLimit, recordRequest, getClientIP } from '@/lib/rate-limiter';

interface AppointmentFormData {
  watchName: string;
  watchItems: string; // 用於多錶款模式
  customerName: string;
  phone: string;
  email: string;
  location: string;
  preferredDate: string;
  notes: string;
  honeypot: string;
  formStartTime: number;
  securityToken: string;
  submissionTime: number;
}

const LOCATION_MAPPING: Record<string, string> = {
  'taipei-main': '台北總店 - 信義區松仁路',
  'taipei-east': '台北東區 - 忠孝東路四段',
  'taichung': '台中店 - 西屯區台灣大道'
};

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request);
    
    // 速率限制檢查
    const rateLimitResult = checkRateLimit(`ip:${clientIP}`);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          message: '請求過於頻繁，請稍後再試',
          resetTime: rateLimitResult.resetTime
        },
        { status: 429 }
      );
    }

    const formData: AppointmentFormData = await request.json();

    // 基本欄位驗證
    const hasWatchData = formData.watchName || formData.watchItems;
    if (!hasWatchData || !formData.customerName || !formData.location || !formData.preferredDate) {
      return NextResponse.json(
        { message: '請填寫所有必填欄位' },
        { status: 400 }
      );
    }

    // 聯絡方式驗證（電話或信箱至少要有一個）
    if (!formData.phone && !formData.email) {
      return NextResponse.json(
        { message: '請至少填寫電話或信箱其中一項' },
        { status: 400 }
      );
    }

    // 電話格式驗證（如果有填寫）
    if (formData.phone) {
      const phoneRegex = /^(\+886|0)?9\d{8}$/;
      if (!phoneRegex.test(formData.phone.replace(/\s+/g, ''))) {
        return NextResponse.json(
          { message: '請輸入正確的手機號碼格式' },
          { status: 400 }
        );
      }
    }

    // 信箱格式驗證（如果有填寫）
    if (formData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        return NextResponse.json(
          { message: '請輸入正確的信箱格式' },
          { status: 400 }
        );
      }
    }

    // 地點驗證
    if (!LOCATION_MAPPING[formData.location]) {
      return NextResponse.json(
        { message: '請選擇有效的鑑賞地點' },
        { status: 400 }
      );
    }

    // 防機器人驗證
    const userAgent = request.headers.get('user-agent') || '';
    const validationResult = await validateSubmission({
      email: formData.email || formData.phone, // 使用信箱或電話作為識別
      formStartTime: formData.formStartTime,
      submissionTime: formData.submissionTime,
      honeypotValue: formData.honeypot,
      userAgent,
      ip: clientIP,
    });

    if (!validationResult.isValid) {
      console.warn('預約表單防機器人驗證失敗:', {
        reason: validationResult.reason,
        riskScore: validationResult.riskScore,
        ip: clientIP,
        userAgent
      });

      return NextResponse.json(
        { message: '表單驗證失敗，請重新整理頁面後再試' },
        { status: 400 }
      );
    }

    // 記錄請求（用於速率限制）
    recordRequest(`ip:${clientIP}`, true);

    // 準備寫入 Google Sheets 的資料
    const timestamp = new Date().toLocaleString('zh-TW', {
      timeZone: 'Asia/Taipei',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 決定錶款資料格式
    const watchData = formData.watchItems || formData.watchName;

    // 格式化預約日期
    const preferredDateFormatted = new Date(formData.preferredDate).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });

    const sheetData = [
      timestamp,                                    // A: 提交時間
      watchData,                                   // B: 預約錶款（單一或多個）
      formData.customerName,                       // C: 客戶姓名
      formData.phone || '',                        // D: 聯絡電話
      formData.email || '',                        // E: 信箱
      LOCATION_MAPPING[formData.location],         // F: 鑑賞地點
      preferredDateFormatted,                      // G: 預約日期
      formData.notes || '',                        // H: 備註
      '待聯繫',                                    // I: 狀態
      '',                                          // J: 預約時間
      '',                                          // K: 處理人員
      clientIP,                                    // L: IP 位址
      userAgent,                                   // M: User Agent
      formData.securityToken,                      // N: 安全 Token
    ];

    // 寫入 Google Sheets
    const sheetId = GOOGLE_SHEETS_CONFIG.getAppointmentSheetId();
    const sheets = getSheetsClient();

    await sheets.spreadsheets.values.append({
      spreadsheetId: sheetId,
      range: '工作表1!A:M',
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: [sheetData],
      },
    });

    console.log('預約表單提交成功:', {
      watchData: watchData,
      customerName: formData.customerName,
      location: LOCATION_MAPPING[formData.location],
      timestamp,
      ip: clientIP
    });

    return NextResponse.json({
      message: '預約申請已送出！我們將盡快與您聯繫確認預約時間。',
      success: true
    });

  } catch (error) {
    console.error('預約表單處理錯誤:', error);
    
    return NextResponse.json(
      { message: '系統錯誤，請稍後再試' },
      { status: 500 }
    );
  }
}

// 處理 OPTIONS 請求（CORS 預檢）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
