import { NextRequest, NextResponse } from 'next/server';
import { appendToSheet } from '@/lib/google-sheets';
import { createTrackingManager } from '@/lib/tracking-manager';
import { clearSessionAvailabilityCache } from '@/lib/session-availability-cache';
import { validateSubmission, type SubmissionData } from '@/lib/anti-bot';
import { checkRateLimit, recordRequest, getClientIP, checkEmailRateLimit, recordEmailRequest } from '@/lib/rate-limiter';

/**
 * 取得 UTC+8 時區的時間字串
 */
function getUTC8TimeString(): string {
  const now = new Date();
  const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  return utc8Time.toISOString().replace('Z', '+08:00');
}

interface RegistrationData {
  eventName: string;
  eventPrice: number;
  sessionTimes: string[];
  customTimeLocation: string;
  participationType: 'individual' | 'pair';
  name: string;
  email: string;
  phone: string;
  companionName: string;
  companionEmail: string;
  gender: 'male' | 'female' | 'other';
  age: '<20' | '20-29' | '30-39' | '40-49' | '50-59' | '>=60';
  region: 'north' | 'central' | 'south' | 'offshore' | 'other_region';
  watchTypes: string[];
  watchBrands: string;
  questions: string;
  agreeToTerms: boolean;
  submittedAt: string;
  needsPayment: boolean;
  // UTM 參數
  utmParams?: {
    utm_campaign: string;
    utm_source_platform: string;
    utm_marketing_tactic: string;
    utm_creative_format: string;
  };
  // 安全驗證資料
  security?: {
    honeypotValue: string;
    formStartTime: number;
    securityToken: string;
    submissionTime: number;
    userAgent: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const data: RegistrationData = await request.json();

    console.log('📝 收到註冊請求:', {
      needsPayment: data.needsPayment,
      eventPrice: data.eventPrice,
      sessionTimes: data.sessionTimes,
      name: data.name,
      utmParams: data.utmParams
    });

    // 記錄 UTM 參數
    if (data.utmParams) {
      console.log('🔗 UTM 追蹤參數:', data.utmParams);
    }

    // 獲取請求的 URL
    const url = request.headers.get('referer') || request.url;

    // 生成訂單號碼
    const orderNo = `pangea_${Date.now()}`;

    // === 安全驗證 ===
    const clientIP = getClientIP(request);
    console.log('🔒 安全驗證開始:', { ip: clientIP, email: data.email });

    // 1. IP 速率限制檢查
    const ipRateLimit = checkRateLimit(`ip:${clientIP}`);
    if (!ipRateLimit.allowed) {
      console.warn('🚫 IP 速率限制觸發:', { ip: clientIP, hits: ipRateLimit.totalHits });
      recordRequest(`ip:${clientIP}`, false);
      return NextResponse.json(
        { error: '請求過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 2. Email 速率限制檢查
    const emailRateLimit = checkEmailRateLimit(data.email);
    if (!emailRateLimit.allowed) {
      console.warn('🚫 Email 速率限制觸發:', { email: data.email, hits: emailRateLimit.totalHits });
      recordEmailRequest(data.email, false);
      return NextResponse.json(
        { error: '此信箱提交過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 3. 安全資料驗證
    if (data.security) {
      const submissionData: SubmissionData = {
        email: data.email,
        formStartTime: data.security.formStartTime,
        submissionTime: data.security.submissionTime,
        honeypotValue: data.security.honeypotValue,
        userAgent: data.security.userAgent,
        ip: clientIP,
      };

      const securityValidation = validateSubmission(submissionData);
      if (!securityValidation.isValid) {
        console.warn('🚫 安全驗證失敗:', {
          reason: securityValidation.reason,
          riskScore: securityValidation.riskScore,
          email: data.email,
          ip: clientIP
        });
        recordRequest(`ip:${clientIP}`, false);
        recordEmailRequest(data.email, false);
        return NextResponse.json(
          { error: '表單驗證失敗，請重新整理頁面後再試' },
          { status: 400 }
        );
      }

      console.log('✅ 安全驗證通過:', {
        riskScore: securityValidation.riskScore,
        email: data.email
      });
    } else {
      // 在測試環境中，缺少安全驗證資料不會阻擋請求
      if (process.env.NODE_ENV === 'test') {
        console.log('🧪 測試環境：跳過安全驗證資料檢查');
      } else {
        console.warn('⚠️ 缺少安全驗證資料');
      }
    }

    // 驗證必填欄位
    const requiredFields = [
      'sessionTimes',
      'participationType',
      'name',
      'email',
      'phone',
      'gender',
      'age',
      'region',
      'watchTypes',
      'agreeToTerms'
    ];

    for (const field of requiredFields) {
      if (!data[field as keyof RegistrationData]) {
        console.error(`缺少必填欄位: ${field}`, data);
        return NextResponse.json(
          { error: `缺少必填欄位: ${field}` },
          { status: 400 }
        );
      }
    }

    // 自訂時間地點改為選填，移除必填檢查
    // 如果有填寫內容，會正常儲存到 Google Sheets

    // 如果是雙人團報，檢查同行者資訊
    if (data.participationType === 'pair') {
      if (!data.companionName.trim() || !data.companionEmail.trim()) {
        return NextResponse.json(
          { error: '雙人團報需要填寫同行者資訊' },
          { status: 400 }
        );
      }
    }

    // 如果有選擇手錶類型且不是"無"，檢查是否填寫了品牌
    if (!data.watchTypes.includes('無') && data.watchTypes.length > 0 && !data.watchBrands.trim()) {
      return NextResponse.json(
        { error: '請填寫手錶品牌' },
        { status: 400 }
      );
    }

    // 準備寫入 Google Sheets 的資料
    // 新欄位配置: A-U為報名基本資料, V-AB為付款相關資料, AC為報名狀態, AD-AE保留, AF為已處理狀態(App Script專用)
    // 欄位順序對應: Submitted at, 場次時間（複選）, 若無合適時間地點..., 參加方式, 姓名, Email, 手機, 同行者姓名, 同行者 Email, 性別, 年齡, 居住地區, 擁有的手錶類型（複選）, 目前擁有手錶的品牌, 對於活動有任何疑問, 個人資料使用與說明同意, url, utm_campaign, utm_source_platform, utm_marketing_tactic, utm_creative_format, 訂單號碼, 付款狀態, 應付金額, PayUni交易號, 付款方式, 付款完成時間, 備註, 報名狀態
    const sheetData = [
      getUTC8TimeString(),                                                 // A: Submitted at (使用 UTC+8 時區)
      data.sessionTimes.join(', '),                                       // B: 場次時間（複選）
      data.customTimeLocation || '',                                      // C: 若無合適時間地點，請留下您期待的時間與地區...
      data.participationType === 'individual' ? '個人報名' : '雙人團報',    // D: 參加方式
      data.name,                                                          // E: 姓名
      data.email,                                                         // F: Email
      data.phone,                                                         // G: 手機
      data.companionName || '',                                           // H: 同行者姓名
      data.companionEmail || '',                                          // I: 同行者 Email
      data.gender === 'male' ? '男' : data.gender === 'female' ? '女' : '其他', // J: 性別
      data.age,                                                           // K: 年齡
      data.region === 'north' ? '北部地區（基、北、桃、竹、苗）' :          // L: 居住地區
      data.region === 'central' ? '中部地區（中、彰、雲、投）' :
      data.region === 'south' ? '南部地區（嘉、南、高、屏）' :
      data.region === 'offshore' ? '外島地區' : 'Other',
      data.watchTypes.join(', '),                                         // M: 擁有的手錶類型（複選）
      data.watchBrands || '',                                             // N: 目前擁有手錶的品牌
      data.questions || '',                                               // O: 對於活動有任何疑問
      data.agreeToTerms ? '是' : '否',                                     // P: 個人資料使用與說明同意
      url,                                                                // Q: url
      data.utmParams?.utm_campaign || '',                                 // R: utm_campaign
      data.utmParams?.utm_source_platform || '',                         // S: utm_source_platform
      data.utmParams?.utm_marketing_tactic || '',                        // T: utm_marketing_tactic
      data.utmParams?.utm_creative_format || '',                         // U: utm_creative_format
      orderNo,                                                            // V: 訂單號碼
      data.needsPayment ? '待付款' : '已完成',                             // W: 付款狀態（PayUni 專用）
      data.needsPayment ? data.eventPrice : 0,                           // X: 應付金額
      '',                                                                 // Y: PayUni交易號
      '',                                                                 // Z: 付款方式
      '',                                                                 // AA: 付款完成時間
      '',                                                                 // AB: 備註
      2                                                                   // AC: 報名狀態（初始為 2=保留中，付款完成後更新為 1=已確認）
      // AD-AE: 保留擴展
      // AF: 已處理狀態 (App Script專用，不在此處寫入)
    ];

    // 寫入 Google Sheets (現在可以連續寫入A:AC，AF欄位保留給App Script)
    try {
      // 使用固定的工作表名稱 "工作表1"
      // 單次寫入 A:AC 範圍 (29個欄位)
      await appendToSheet(`工作表1!A:AC`, [sheetData]);

      // 清除場次名額快取，確保下次查詢時獲取最新資料
      clearSessionAvailabilityCache();
    } catch (error) {
      console.error('寫入 Google Sheets 失敗:', error);
      // 即使 Google Sheets 寫入失敗，我們仍然繼續處理
    }

    // 郵件發送功能已移除，改由 App Script 處理

    // 發送追蹤事件 - 註冊完成改為 InitiateCheckout
    try {
      const tracker = createTrackingManager(request);
      await tracker.trackInitiateCheckout({
        formName: 'event_registration',
        value: data.eventPrice || 1500,
        currency: 'TWD',
        email: data.email,
        phone: data.phone,
        userId: data.email,
        orderId: orderNo,
        productName: data.eventName,
        category: 'event',
        customData: {
          event_name: data.eventName,
          participation_type: data.participationType,
          session_times: data.sessionTimes.join(', '),
          needs_payment: data.needsPayment,
        },
      });
    } catch (error) {
      console.error('發送追蹤事件失敗:', error);
    }

    // 記錄成功的請求
    recordRequest(`ip:${clientIP}`, true);
    recordEmailRequest(data.email, true);
    console.log('✅ 報名處理完成:', { orderNo, email: data.email, needsPayment: data.needsPayment });

    return NextResponse.json({
      success: true,
      orderNo,
      needsPayment: data.needsPayment,
      message: data.needsPayment ? '資料已記錄，請繼續完成付款' : '報名成功！我們會盡快與您聯繫。'
    });

  } catch (error) {
    console.error('處理報名請求時發生錯誤:', error);

    // 記錄失敗的請求（如果有 IP 資訊）
    try {
      const clientIP = getClientIP(request);
      recordRequest(`ip:${clientIP}`, false);
    } catch (ipError) {
      console.error('無法記錄失敗請求:', ipError);
    }

    return NextResponse.json(
      { error: '伺服器錯誤，請稍後再試' },
      { status: 500 }
    );
  }
}


