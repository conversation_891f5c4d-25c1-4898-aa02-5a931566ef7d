import { NextRequest, NextResponse } from 'next/server';
import { getSheetsClient } from '@/lib/google-sheets';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';
import { getClientIP, checkRateLimit, recordRequest, recordEmailRequest } from '@/lib/rate-limiter';
import { validateSubmission, type SubmissionData } from '@/lib/anti-bot';

// Pangea 預約資料介面
interface PangeaAppointmentData {
  // 基本資料
  name: string;
  email: string;
  phone: string;
  gender: string;
  
  // 預約資訊
  appointmentLocation: string;
  appointmentTime: string;
  age: string;
  region: string;
  
  // 手錶相關
  watchTypes: string[];
  watchBrands: string;
  questions: string;
  
  // UTM 參數
  utmParams?: {
    utm_campaign: string;
    utm_source_platform: string;
    utm_marketing_tactic: string;
    utm_creative_format: string;
  };
  
  // 安全驗證資料
  security?: {
    honeypotValue: string;
    formStartTime: number;
    securityToken: string;
    submissionTime: number;
    userAgent: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const data: PangeaAppointmentData = await request.json();

    console.log('📝 收到 Pangea 預約請求:', {
      name: data.name,
      email: data.email,
      appointmentLocation: data.appointmentLocation,
      appointmentTime: data.appointmentTime,
      utmParams: data.utmParams
    });

    // 記錄 UTM 參數
    if (data.utmParams) {
      console.log('🔗 UTM 追蹤參數:', data.utmParams);
    }

    // 獲取請求的 URL
    const url = request.headers.get('referer') || request.url;

    // === 安全驗證 ===
    const clientIP = getClientIP(request);
    console.log('🔒 安全驗證開始:', { ip: clientIP, email: data.email });

    // 1. IP 速率限制檢查
    const ipRateLimit = checkRateLimit(`ip:${clientIP}`);
    if (!ipRateLimit.allowed) {
      console.warn('🚫 IP 速率限制觸發:', { ip: clientIP, hits: ipRateLimit.totalHits });
      recordRequest(`ip:${clientIP}`, false);
      return NextResponse.json(
        { error: '請求過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 2. Email 速率限制檢查
    const emailRateLimit = checkRateLimit(`email:${data.email}`);
    if (!emailRateLimit.allowed) {
      console.warn('🚫 Email 速率限制觸發:', { email: data.email, hits: emailRateLimit.totalHits });
      recordRequest(`ip:${clientIP}`, false);
      recordEmailRequest(data.email, false);
      return NextResponse.json(
        { error: '此信箱提交過於頻繁，請稍後再試' },
        { status: 429 }
      );
    }

    // 3. 安全資料驗證
    if (data.security) {
      const submissionData: SubmissionData = {
        email: data.email,
        formStartTime: data.security.formStartTime,
        submissionTime: data.security.submissionTime,
        honeypotValue: data.security.honeypotValue,
        userAgent: data.security.userAgent,
        ip: clientIP,
      };

      const securityValidation = validateSubmission(submissionData);
      if (!securityValidation.isValid) {
        console.warn('🚫 安全驗證失敗:', {
          reason: securityValidation.reason,
          riskScore: securityValidation.riskScore,
          email: data.email,
          ip: clientIP
        });
        recordRequest(`ip:${clientIP}`, false);
        recordEmailRequest(data.email, false);
        return NextResponse.json(
          { error: '表單驗證失敗，請重新整理頁面後再試' },
          { status: 400 }
        );
      }

      console.log('✅ 安全驗證通過:', {
        riskScore: securityValidation.riskScore,
        email: data.email
      });
    } else {
      // 在測試環境中，缺少安全驗證資料不會阻擋請求
      if (process.env.NODE_ENV === 'test') {
        console.log('🧪 測試環境：跳過安全驗證資料檢查');
      } else {
        console.warn('⚠️ 缺少安全驗證資料');
      }
    }

    // === 資料驗證 ===
    if (!data.name || !data.email || !data.phone || !data.appointmentLocation || !data.appointmentTime) {
      return NextResponse.json(
        { error: '請填寫所有必填欄位' },
        { status: 400 }
      );
    }

    // Email 格式驗證
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return NextResponse.json(
        { error: '請輸入有效的電子郵件地址' },
        { status: 400 }
      );
    }

    // 電話格式驗證
    const phoneRegex = /^(09\d{8}|886\d{9,10})$/;
    if (!phoneRegex.test(data.phone.replace(/\D/g, ''))) {
      return NextResponse.json(
        { error: '請輸入有效的電話號碼' },
        { status: 400 }
      );
    }

    // === 寫入 Google Sheets ===
    const sheetId = GOOGLE_SHEETS_CONFIG.getPangeaAppointmentSheetId();
    const sheets = getSheetsClient();

    // 準備要寫入的資料 (A-R 欄位)
    const submittedAt = new Date().toLocaleString('zh-TW', { timeZone: 'Asia/Taipei' });
    const watchTypesString = data.watchTypes.join(', ');
    
    const rowData = [
      submittedAt,                                    // A: Submitted at
      data.name,                                      // B: 姓名
      data.email,                                     // C: Email
      data.phone,                                     // D: 手機
      data.gender,                                    // E: 性別
      data.appointmentLocation,                       // F: 預約地點
      data.appointmentTime,                          // G: 預約時間
      data.age,                                      // H: 年齡
      data.region,                                   // I: 居住地區
      watchTypesString,                              // J: 擁有的手錶類型（複選）
      data.watchBrands,                              // K: 目前擁有手錶的品牌
      data.questions || '',                          // L: 對於預約體驗有任何疑問
      url,                                           // M: url
      data.utmParams?.utm_campaign || '',            // N: utm_campaign
      data.utmParams?.utm_source_platform || '',     // O: utm_source_platform
      data.utmParams?.utm_marketing_tactic || '',    // P: utm_marketing_tactic
      data.utmParams?.utm_creative_format || '',     // Q: utm_creative_format
      ''                                             // R: 備註
      // S、T、U 欄位：保留供未來使用
      // V 欄位：專門給 App Script 處理，請勿修改
    ];

    console.log('📊 準備寫入 Google Sheets:', { sheetId, rowData: rowData.slice(0, 5) });

    await sheets.spreadsheets.values.append({
      spreadsheetId: sheetId,
      range: '工作表1!A:R',
      valueInputOption: 'RAW',
      requestBody: {
        values: [rowData],
      },
    });

    console.log('✅ 資料已成功寫入 Google Sheets');

    // 記錄成功的請求
    recordRequest(`ip:${clientIP}`, true);
    recordEmailRequest(data.email, true);
    console.log('✅ Pangea 預約處理完成:', { email: data.email });

    return NextResponse.json({
      success: true,
      message: '預約申請已送出！我們會盡快與您聯繫安排體驗時間。'
    });

  } catch (error) {
    console.error('處理 Pangea 預約請求時發生錯誤:', error);

    // 記錄失敗的請求（如果有 IP 資訊）
    try {
      const clientIP = getClientIP(request);
      recordRequest(`ip:${clientIP}`, false);
    } catch (ipError) {
      console.error('無法記錄失敗請求:', ipError);
    }

    return NextResponse.json(
      { error: '伺服器錯誤，請稍後再試' },
      { status: 500 }
    );
  }
}
