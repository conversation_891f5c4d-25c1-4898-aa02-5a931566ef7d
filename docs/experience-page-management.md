# Experience 頁面管理文件

## 概述

`/experience` 頁面是錶匠體驗活動的報名頁面，包含活動介紹、場次管理、人數限制控制等功能。本文件說明如何管理和修改相關設定。

## 檔案結構

```
src/
├── app/experience/page.tsx          # 主要頁面組件
├── app/api/event-registration/      # 報名 API
├── app/api/session-availability/    # 場次名額查詢 API
├── hooks/useSessionAvailability.ts  # 場次名額 Hook
└── lib/google-sheets-utils.ts       # Google Sheets 工具函數
```

## 活動基本設定

### 1. 活動資訊配置

在 `src/app/experience/page.tsx` 中的 `eventConfig` 物件：

```typescript
const eventConfig = {
  name: '錶匠體驗機芯拆解',    // 活動名稱
  price: 1500,                // 個人報名價格（雙人團報會自動 x2）
  sessionOptions: [           // 可選場次列表
    '台中 07/18（五）19:20',
    '台北 07/19（六）13:20',
    '台北 07/20（日）13:20',
    '台北 07/20（日）15:20',
    '台北 07/25（五）19:20',
    '台北 07/26（六）13:20',
    '台北 07/26（六）15:20',
    '有意願但無合適時間地點'
  ],
  // 每個場次的名額上限配置
  sessionCapacity: {
    '台中 07/18（五）19:20': 8,
    '台北 07/19（六）13:20': 8,
    '台北 07/20（日）13:20': 8,
    '台北 07/20（日）15:20': 8,
    '台北 07/25（五）19:20': 8,
    '台北 07/26（六）13:20': 8,
    '台北 07/26（六）15:20': 8,
    '有意願但無合適時間地點': 999 // 無限制
  }
};
```

### 2. 活動內容配置

在同一檔案中的 `activitySections` 陣列：

```typescript
const activitySections = [
  {
    id: 'power-transmission',
    title: '動力傳導',
    icon: 'Zap', // Lucide React 圖標名稱
    points: [
      '感受金屬發條緊縮的張力...',
      '觀察齒輪、分針、時針如何透過精密齒輪的傳動運作...',
      // 更多說明點
    ]
  },
  {
    id: 'escapement',
    title: '擺輪擒縱',
    icon: 'Cog',
    points: [
      '發條盒如何與擒縱器？...',
      // 更多說明點
    ]
  }
  // 可以新增更多區塊
];
```

## 人數限制管理

### 1. 場次名額設定

**位置：** `src/app/experience/page.tsx` 和 `src/app/api/session-availability/route.ts`

兩個檔案中的 `SESSION_CAPACITY` 物件必須保持一致：

```typescript
const SESSION_CAPACITY = {
  '台中 07/18（五）19:20': 8,
  '台北 07/19（六）13:20': 8,
  '台北 07/20（日）13:20': 8,
  '台北 07/20（日）15:20': 8,
  '台北 07/25（五）19:20': 8,
  '台北 07/26（六）13:20': 8,
  '台北 07/26（六）15:20': 8,
  '有意願但無合適時間地點': 999 // 無限制
};
```

### 2. 人數計算邏輯

系統會自動從 Google Sheets 讀取報名資料並計算：

- **個人報名**：佔用 1 個名額
- **雙人團報**：佔用 2 個名額
- **報名狀態**：只計算「已確認」(1) 和「保留中」(2) 的報名，「已取消」(3) 不計入

### 3. 額滿判定

- 當 `剩餘名額 <= 0` 時，該場次顯示「已額滿」
- 當 `剩餘名額 < 3` 且 `> 0` 時，顯示「剩餘 X 名額」
- 其他情況不顯示名額資訊

## 場次與地點管理

### 1. 新增場次

1. 在 `eventConfig.sessionOptions` 陣列中新增場次
2. 在 `eventConfig.sessionCapacity` 物件中設定該場次的名額上限
3. 在 `src/app/api/session-availability/route.ts` 的 `SESSION_CAPACITY` 中同步新增
4. 在頁面的場次顯示區域手動更新時間資訊

### 2. 修改場次時間

直接修改 `sessionOptions` 陣列中的場次名稱，系統會自動同步。

### 3. 活動地點設定

在頁面的「活動地點」區塊中修改：

```typescript
<div className="flex items-start">
  <MapPin className="w-4 h-4 md:w-4 md:h-4 mr-2 mt-0.5 flex-shrink-0" />
  <a
    href="https://maps.google.com/?q=台北市信義區光復南路501號3F"
    target="_blank"
    rel="noopener noreferrer"
    className="text-sm md:text-base hover:underline"
  >
    台北市信義區光復南路 501 號 3F
  </a>
</div>
```

## Google Sheets 資料結構

### 報名資料欄位對應

報名資料會寫入 Google Sheets 的「工作表1」，欄位對應如下：

| 欄位 | 內容 | 說明 |
|------|------|------|
| A | 提交時間 | UTC+8 時區 |
| B | 場次時間 | 複選場次，逗號分隔 |
| C | 自訂時間地點 | 選填 |
| D | 參加方式 | 個人報名/雙人團報 |
| E | 姓名 | |
| F | Email | |
| G | 手機 | |
| H | 同行者姓名 | 雙人團報時填寫 |
| I | 同行者 Email | 雙人團報時填寫 |
| J | 性別 | 男/女/其他 |
| K | 年齡 | 年齡區間 |
| L | 居住地區 | |
| M | 手錶類型 | 複選，逗號分隔 |
| N | 手錶品牌 | |
| O | 問題 | |
| P | 同意條款 | 是/否 |
| Q | URL | 報名來源頁面 |
| R-U | UTM 參數 | 追蹤參數 |
| V | 訂單號碼 | pangea_timestamp 格式 |
| W | 付款狀態 | 待付款/已完成 |
| X | 應付金額 | |
| Y-AB | 付款相關 | PayUni 交易資訊 |
| AC | 報名狀態 | 1=已確認, 2=保留中, 3=已取消 |

### 報名狀態說明

- **1 (已確認)**：付款完成，計入名額
- **2 (保留中)**：已報名未付款，計入名額
- **3 (已取消)**：已取消報名，不計入名額

## 快取機制

### 場次名額快取

- **快取時間**：30 秒
- **快取位置**：記憶體快取
- **清除時機**：新報名提交後自動清除

### 手動清除快取

如需手動清除快取，可以透過 API 呼叫：

```javascript
// 清除場次名額快取
import { clearSessionAvailabilityCache } from '@/lib/session-availability-cache';
clearSessionAvailabilityCache();
```

## 常見管理任務

### 1. 修改活動價格

在 `eventConfig.price` 修改個人報名價格，雙人團報會自動計算為 `price * 2`。

### 2. 新增活動場次

1. 更新 `eventConfig.sessionOptions`
2. 更新 `eventConfig.sessionCapacity`
3. 同步更新 API 中的 `SESSION_CAPACITY`
4. 更新頁面顯示的場次時間列表

### 3. 調整場次名額

直接修改 `sessionCapacity` 物件中對應場次的數值。

### 4. 查看報名狀況

透過 Google Sheets 查看「工作表1」，或使用場次名額 API：

```
GET /api/session-availability
```

### 5. 手動調整報名狀態

在 Google Sheets 的 AC 欄位修改報名狀態：
- 改為 `3` 可釋放名額（已取消）
- 改為 `1` 確認報名（已確認）
- 改為 `2` 保留名額（保留中）

## 注意事項

1. **資料一致性**：修改場次設定時，務必同時更新前端和 API 的配置
2. **快取延遲**：名額變更可能有 30 秒的快取延遲
3. **Google Sheets 權限**：確保服務帳戶有讀寫權限
4. **測試環境**：修改前建議在測試環境先驗證功能

## 故障排除

### 場次顯示「已額滿」但實際有名額

1. 檢查 Google Sheets AC 欄位的報名狀態
2. 清除場次名額快取
3. 確認前端和 API 的 `SESSION_CAPACITY` 設定一致

### 報名無法提交

1. 檢查 Google Sheets 寫入權限
2. 確認必填欄位驗證邏輯
3. 查看瀏覽器控制台錯誤訊息

### 名額計算錯誤

1. 檢查 Google Sheets 資料格式
2. 確認雙人團報的名額計算邏輯
3. 驗證報名狀態欄位 (AC) 的數值