# 電商系統設計文檔

## 概述

基於現有的 Next.js 網站架構，設計一個完整的電商系統，包含商品管理、購物車、會員系統、訂單處理、金流物流串接和管理後台。系統採用現代化的全端架構，優先考慮開發效率和未來擴展性。

## 架構設計

### 技術棧選擇

**前端：**
- Next.js 15 (App Router)
- React 19
- TypeScript
- Tailwind CSS
- Radix UI 組件庫

**後端方案建議：Cloudflare D1 + Better Auth + Next.js API Routes**

**選擇理由：**
1. **完全免費**：Cloudflare D1 目前免費額度非常大方
2. **全球邊緣運算**：更低的延遲和更好的效能
3. **現代化認證**：Better Auth 提供更靈活的認證解決方案
4. **生態系統整合**：與 Cloudflare Pages、R2 等服務完美整合
5. **無冷啟動問題**：不像其他免費資料庫服務會休眠

**資料庫：** Cloudflare D1 (SQLite)
**認證系統：** Better Auth
**檔案儲存：** Cloudflare R2
**部署平台：** Cloudflare Pages
**金流串接：** 統一金流 API
**物流串接：** 7-11、全家、宅配通等 API

### 系統架構圖

```mermaid
graph TB
    A[用戶瀏覽器] --> B[Next.js Frontend]
    B --> C[Next.js API Routes]
    B --> D[Better Auth Client]
    
    C --> E[統一金流 API]
    C --> F[物流 API]
    C --> G[Email 服務]
    C --> H[Cloudflare D1]
    
    D --> I[Better Auth Server]
    I --> H
    
    J[Cloudflare R2] --> B
    
    K[管理後台] --> B
    
    subgraph "外部服務"
        E
        F
        G
    end
    
    subgraph "Cloudflare 服務"
        H
        I
        J
    end
```

## 資料模型設計

### 核心資料表（Cloudflare D1 / SQLite）

```sql
-- 用戶表（Better Auth 管理）
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    email_verified INTEGER DEFAULT 0,
    name TEXT,
    image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用戶會話表（Better Auth）
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    token TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 分類表
CREATE TABLE categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 商品表
CREATE TABLE products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price REAL NOT NULL,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    image_urls TEXT, -- JSON string array
    category_id TEXT,
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- 購物車表
CREATE TABLE cart_items (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE(user_id, product_id)
);

-- 訂單表
CREATE TABLE orders (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    order_number TEXT UNIQUE NOT NULL,
    status TEXT DEFAULT 'pending',
    total_amount REAL NOT NULL,
    shipping_address TEXT, -- JSON string
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    shipping_method TEXT,
    shipping_fee REAL DEFAULT 0,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 訂單項目表
CREATE TABLE order_items (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    product_id TEXT,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    total_price REAL NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- 用戶資料表
CREATE TABLE user_profiles (
    id TEXT PRIMARY KEY,
    user_id TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone TEXT,
    address TEXT, -- JSON string
    birth_date TEXT,
    member_level TEXT DEFAULT 'bronze',
    total_spent REAL DEFAULT 0,
    points INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 物流追蹤表
CREATE TABLE shipping_tracking (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    tracking_number TEXT,
    carrier TEXT,
    status TEXT,
    status_description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);
```

### 未來擴展表（預留）

```sql
-- 折扣碼表
CREATE TABLE discount_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_type VARCHAR(20), -- 'percentage' or 'fixed'
    discount_value DECIMAL(10,2),
    min_order_amount DECIMAL(10,2),
    max_uses INTEGER,
    used_count INTEGER DEFAULT 0,
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 活動表
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    promotion_type VARCHAR(50),
    conditions JSONB,
    rewards JSONB,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 組件與介面設計

### 前端組件架構

```
src/
├── components/
│   ├── ecommerce/
│   │   ├── ProductCard.tsx
│   │   ├── ProductList.tsx
│   │   ├── ShoppingCart.tsx
│   │   ├── CartItem.tsx
│   │   ├── CheckoutForm.tsx
│   │   └── OrderSummary.tsx
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── UserProfile.tsx
│   └── admin/
│       ├── ProductManager.tsx
│       ├── OrderManager.tsx
│       └── Dashboard.tsx
├── app/
│   ├── products/
│   │   ├── page.tsx
│   │   └── [id]/page.tsx
│   ├── cart/
│   │   └── page.tsx
│   ├── checkout/
│   │   └── page.tsx
│   ├── orders/
│   │   └── page.tsx
│   ├── profile/
│   │   └── page.tsx
│   ├── admin/
│   │   ├── page.tsx
│   │   ├── products/page.tsx
│   │   └── orders/page.tsx
│   └── api/
│       ├── products/
│       ├── cart/
│       ├── orders/
│       ├── payment/
│       └── shipping/
└── lib/
    ├── db.ts          -- Cloudflare D1 client
    ├── auth.ts        -- Better Auth configuration
    ├── payment.ts     -- 統一金流整合
    └── shipping.ts    -- 物流 API 整合
```

### API 端點設計

**商品相關：**
- `GET /api/products` - 取得商品列表
- `GET /api/products/[id]` - 取得單一商品
- `POST /api/products` - 新增商品（管理員）
- `PUT /api/products/[id]` - 更新商品（管理員）
- `DELETE /api/products/[id]` - 刪除商品（管理員）

**購物車相關：**
- `GET /api/cart` - 取得購物車內容
- `POST /api/cart/add` - 加入商品到購物車
- `PUT /api/cart/update` - 更新購物車項目
- `DELETE /api/cart/remove` - 移除購物車項目

**訂單相關：**
- `POST /api/orders/create` - 建立訂單
- `GET /api/orders` - 取得用戶訂單列表
- `GET /api/orders/[id]` - 取得訂單詳情
- `PUT /api/orders/[id]/status` - 更新訂單狀態（管理員）

**支付相關：**
- `POST /api/payment/create` - 建立付款
- `POST /api/payment/callback` - 付款回調處理
- `GET /api/payment/status/[orderId]` - 查詢付款狀態

**物流相關：**
- `POST /api/shipping/create` - 建立物流單
- `GET /api/shipping/track/[trackingNumber]` - 查詢物流狀態
- `POST /api/shipping/webhook` - 物流狀態更新 webhook

## 錯誤處理

### 前端錯誤處理

```typescript
// 統一錯誤處理 Hook
export function useErrorHandler() {
  const handleError = (error: Error) => {
    console.error('Application Error:', error);
    
    // 根據錯誤類型顯示不同訊息
    if (error.message.includes('network')) {
      toast.error('網路連線異常，請稍後再試');
    } else if (error.message.includes('auth')) {
      toast.error('請重新登入');
      // 重導向到登入頁面
    } else {
      toast.error('系統發生錯誤，請聯繫客服');
    }
  };
  
  return { handleError };
}
```

### 後端錯誤處理

```typescript
// API 錯誤處理中間件
export function withErrorHandler(handler: NextApiHandler) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      return await handler(req, res);
    } catch (error) {
      console.error('API Error:', error);
      
      if (error instanceof ValidationError) {
        return res.status(400).json({ error: error.message });
      }
      
      if (error instanceof AuthError) {
        return res.status(401).json({ error: '未授權訪問' });
      }
      
      return res.status(500).json({ error: '伺服器內部錯誤' });
    }
  };
}
```

## 測試策略

### 單元測試
- **組件測試**：使用 Jest + React Testing Library
- **API 測試**：測試所有 API 端點的正確性
- **業務邏輯測試**：購物車計算、訂單處理等核心邏輯

### 整合測試
- **支付流程測試**：模擬完整的支付流程
- **物流整合測試**：測試物流 API 串接
- **資料庫操作測試**：測試 CRUD 操作的正確性

### E2E 測試
- **用戶購物流程**：從瀏覽商品到完成訂單
- **管理員操作流程**：商品管理、訂單處理
- **會員功能測試**：註冊、登入、個人資料管理

### 測試環境設定

```typescript
// 測試用的 Supabase 配置
export const supabaseTest = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_TEST_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_TEST_ANON_KEY!
);

// 測試資料清理
export async function cleanupTestData() {
  await supabaseTest.from('order_items').delete().neq('id', '');
  await supabaseTest.from('orders').delete().neq('id', '');
  await supabaseTest.from('cart_items').delete().neq('id', '');
  await supabaseTest.from('products').delete().neq('id', '');
}
```

## 安全性考量

### 認證與授權
- **JWT Token**：使用 Supabase Auth 的 JWT token
- **Row Level Security**：資料庫層級的權限控制
- **API 路由保護**：驗證用戶身份和權限

### 資料驗證
- **前端驗證**：即時表單驗證提升用戶體驗
- **後端驗證**：所有 API 端點都進行資料驗證
- **SQL 注入防護**：使用參數化查詢

### 支付安全
- **HTTPS 強制**：所有支付相關請求使用 HTTPS
- **敏感資料加密**：信用卡資訊不存儲在本地
- **交易驗證**：每筆交易都進行雙重驗證

## 效能優化

### 前端優化
- **圖片優化**：使用 Next.js Image 組件
- **代碼分割**：按路由分割 JavaScript 包
- **快取策略**：商品資料使用 SWR 快取

### 後端優化
- **資料庫索引**：為常用查詢建立索引
- **API 快取**：使用 Redis 快取熱門商品資料
- **CDN 部署**：靜態資源使用 CDN 加速

### 資料庫優化

```sql
-- 建立必要的索引（SQLite）
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_cart_user ON cart_items(user_id);
CREATE INDEX idx_sessions_token ON sessions(token);
CREATE INDEX idx_sessions_user ON sessions(user_id);
```

## 部署與維運

### 部署架構
- **前端**：Cloudflare Pages（推薦）或 Vercel
- **資料庫**：Cloudflare D1
- **檔案儲存**：Cloudflare R2
- **CDN**：Cloudflare 全球邊緣網路

### 監控與日誌
- **錯誤追蹤**：整合 Sentry 或類似服務
- **效能監控**：使用 Vercel Analytics
- **業務指標**：訂單轉換率、平均訂單金額等

### 備份策略
- **資料庫備份**：定期匯出 D1 資料庫到 R2 儲存
- **程式碼備份**：Git 版本控制
- **設定檔備份**：環境變數和設定檔的安全備份
- **自動化備份**：使用 Cloudflare Workers Cron 定期備份